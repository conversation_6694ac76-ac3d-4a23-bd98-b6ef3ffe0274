import React, { useState, useRef } from "react";
import {
  HiTrash,
  HiPlus,
  HiChevronLeft,
  HiChevronRight,
} from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { getConfidenceColor } from "../../utils/colorUtils";
import PickDetailModal from "../../components/PicksView/PickDetailModal";
import HandicapperAvatars from "../../components/HandicapperAvatars";
import { useAuth } from "../../contexts/AuthContext";
import { useHandicapperProfile } from "../../contexts/HandicapperProfileContext";
import { generateHandicapperIdFromName } from "../../utils/dataTransforms";

interface Pick {
  id: string;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
  handicapperName?: string;
  handicapperNames?: string[]; // Array of all handicappers who made this pick
}

interface HeroSectionProps {
  totalPicksCount: number;
  userPicks: Pick[];
  isOptimizing: boolean;
  optimizerError: string;
  onAddPicksClick: () => void;
  onOptimizeParlays: () => void;
  onRemovePick: (id: string) => void;
}

// Pick Card Component
function PickCard({
  pick,
  onRemovePick,
  onPickClick,
  onHandicapperClick,
}: {
  pick: Pick;
  onRemovePick: (id: string) => void;
  onPickClick: (pick: Pick) => void;
  onHandicapperClick: (handicapperName: string) => void;
}) {
  return (
    <div
      className="bg-[#233e6c] rounded-xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 ease-linear hover:cursor-pointer hover:scale-[101.5%] min-h-[200px] sm:min-h-[240px]"
      onClick={() => onPickClick(pick)}
    >
      <div className="flex flex-row gap-4 sm:gap-6 my-auto justify-center items-center h-full">
        {/* Player info */}
        <div className="flex flex-col items-center md:flex-1 min-w-0">
          <div
            className="w-24 h-24 sm:w-32 sm:h-32 rounded-full mb-2 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
            style={{
              border: `4px solid ${getConfidenceColor(pick.confidence || 75)}`,
            }}
          >
            <IoShirtOutline
              className="w-16 h-16 sm:w-20 sm:h-20 absolute"
              style={{ color: getConfidenceColor(pick.confidence || 75) }}
            />
            <div className="text-white font-bold text-base sm:text-lg md:text-xl z-10 relative">
              {pick.playerNumber}
            </div>
          </div>
          <h3 className="font-bold text-sm sm:text-lg text-center">
            {pick.playerName}
          </h3>
          <p className="text-xs sm:text-sm text-center text-white">
            {pick.betType}
          </p>
          <p className="text-xs text-gray-400 text-center">{pick.gameInfo}</p>

          {/* Handicapper display - use HandicapperAvatars component */}
          {((pick.handicapperNames && pick.handicapperNames.length > 0) ||
            pick.handicapperName) && (
            <div className="mt-2 w-full">
              <HandicapperAvatars
                handicapperNames={
                  pick.handicapperNames && pick.handicapperNames.length > 0
                    ? pick.handicapperNames
                    : pick.handicapperName
                    ? [pick.handicapperName]
                    : []
                }
                maxVisible={2}
                size="sm"
                showLabel={false}
                onHandicapperClick={onHandicapperClick}
              />
            </div>
          )}
        </div>

        {/* Confidence and remove button */}
        <div className="flex flex-col items-center gap-2 md:flex-1 min-w-0 m-auto h-full">
          <div className="text-center flex flex-col items-center justify-start h-full">
            <div
              className="text-[50px] sm:text-[75px] md:text-[100px] font-bold mt-[-12px] sm:mt-[-24px]"
              style={{ color: getConfidenceColor(pick.confidence || 75) }}
            >
              {pick.confidence || 75}
            </div>
            <div className="text-[18px] sm:text-[24px] font-bold text-white mt-[-12px] sm:mt-[-24px]">
              Confidence
              <br />
              Rating
            </div>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation(); // Prevent modal from opening when remove button is clicked
              onRemovePick(pick.id);
            }}
            className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg hover:cursor-pointer"
            title="Remove pick"
          >
            <HiTrash className="w-6 h-6 sm:w-8 sm:h-8 hover:cursor-pointer" />
          </button>
        </div>
      </div>
    </div>
  );
}

function HeroSection({
  totalPicksCount,
  userPicks,
  isOptimizing,
  optimizerError,
  onAddPicksClick,
  onOptimizeParlays,
  onRemovePick,
}: HeroSectionProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  // Modal state management
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPick, setSelectedPick] = useState<Pick | null>(null);

  // Contexts for handicapper navigation
  const { navigateToView } = useAuth();
  const { selectHandicapper } = useHandicapperProfile();

  // Handle handicapper click for navigation
  const handleHandicapperClick = (handicapperName: string) => {
    const handicapperId = generateHandicapperIdFromName(handicapperName);
    selectHandicapper(handicapperId);
    navigateToView("handicapperProfile");
  };

  // Transform homepage pick to modal-expected format
  const transformPickForModal = (pick: Pick) => {
    // Extract numeric ID from string ID (e.g., "pick_123" -> 123)
    const numericId = parseInt(pick.id.replace(/\D/g, "")) || 1;

    // Use handicapperNames array if available, otherwise fall back to single handicapperName
    const handicapperNames =
      pick.handicapperNames && pick.handicapperNames.length > 0
        ? pick.handicapperNames
        : pick.handicapperName
        ? [pick.handicapperName]
        : [];
    const expertCount = handicapperNames.length || 1;

    return {
      id: numericId,
      playerName: pick.playerName,
      playerNumber: pick.playerNumber,
      betType: pick.betType,
      gameInfo: pick.gameInfo,
      confidence: pick.confidence || 75,
      expertCount: expertCount, // Use actual count based on handicapper names
      additionalExperts: 0, // Default value for homepage picks
      handicapperNames: handicapperNames, // Properly populated array for "Picked By" section
    };
  };

  // Handle pick card click to open modal
  const handlePickClick = (pick: Pick) => {
    setSelectedPick(pick);
    setIsModalOpen(true);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPick(null);
  };

  // Handle scroll navigation
  const handleScroll = (direction: "left" | "right") => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const scrollAmount = container.offsetWidth * 0.5; // Scroll by 50% of container width (one column)

    if (direction === "left") {
      container.scrollBy({ left: -scrollAmount, behavior: "smooth" });
    } else {
      container.scrollBy({ left: scrollAmount, behavior: "smooth" });
    }
  };

  // Update scroll button states
  const updateScrollButtons = () => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const { scrollLeft, scrollWidth, clientWidth } = container;

    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
  };

  // Handle scroll event to update button states
  const handleScrollEvent = () => {
    updateScrollButtons();
  };
  return (
    <div className="min-h-screen bg-[#051844] text-[#58C612] flex flex-col p-4 sm:p-6 select-none">
      {/* Logo Section */}
      <div className="w-full flex justify-center pt-20 sm:pt-24 md:pt-28 lg:pt-32 xl:pt-36 pb-6 sm:pb-8">
        <header className="flex items-center justify-center">
          <img
            src="/project_parlay_logo.png"
            alt="Project Parlay Logo"
            className="w-auto h-24 sm:h-32 md:h-36 lg:h-40 select-none transition-all duration-300 ease-in-out"
          />
        </header>
      </div>

      {totalPicksCount === 0 ? (
        // Empty state
        <div className="flex-1 flex flex-col items-center justify-center text-center px-4 py-8">
          <h2 className="text-white text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
            Time for a fresh start
          </h2>

          <p className="text-white text-lg sm:text-xl md:text-2xl mb-8 max-w-2xl">
            You don't have any picks in your list
          </p>

          <button
            onClick={onAddPicksClick}
            className="px-8 py-4 bg-[#233e6c] hover:bg-[#232d6c] text-white font-bold rounded-lg text-lg sm:text-xl md:text-2xl transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer max-w-sm w-full"
          >
            Add Picks
          </button>
        </div>
      ) : (
        // My Picks display
        <div className="flex-1 w-full max-w-7xl mx-auto px-4 flex flex-col min-h-0">
          <div className="mb-4 sm:mb-6 flex items-center justify-between">
            {/* Left: My Picks text */}
            <div className="flex-1">
              <h2 className="text-white text-2xl sm:text-3xl md:text-4xl font-bold">
                My Picks ({totalPicksCount})
              </h2>
            </div>

            {/* Center: Arrow navigation controls (only show when userPicks.length > 8) */}
            <div className="flex-1 flex justify-center">
              {userPicks.length > 8 && (
                <div className="flex items-center gap-2">
                  {/* Left Arrow */}
                  <button
                    onClick={() => handleScroll("left")}
                    disabled={!canScrollLeft}
                    className={`p-3 rounded-full shadow-lg transition-all duration-300 ${
                      canScrollLeft
                        ? "bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:scale-105 cursor-pointer"
                        : "bg-gray-700 text-gray-500 cursor-not-allowed opacity-50"
                    }`}
                    title="Scroll left"
                  >
                    <HiChevronLeft className="w-6 h-6" />
                  </button>

                  {/* Right Arrow */}
                  <button
                    onClick={() => handleScroll("right")}
                    disabled={!canScrollRight}
                    className={`p-3 rounded-full shadow-lg transition-all duration-300 ${
                      canScrollRight
                        ? "bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:scale-105 cursor-pointer"
                        : "bg-gray-700 text-gray-500 cursor-not-allowed opacity-50"
                    }`}
                    title="Scroll right"
                  >
                    <HiChevronRight className="w-6 h-6" />
                  </button>
                </div>
              )}
            </div>

            {/* Right: Add More Picks button */}
            <div className="flex-1 flex justify-end">
              <button
                onClick={onAddPicksClick}
                className="px-6 py-3 sm:py-4 bg-[#233e6c] hover:bg-[#1a2d54] text-white font-bold rounded-lg text-base sm:text-lg transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer flex items-center gap-2"
              >
                <HiPlus className="w-5 h-5" />
                Add More Picks
              </button>
            </div>
          </div>

          {/* Responsive picks container with 2-column grid expanding to 4 rows, then horizontal scrolling */}
          <div className="flex-1 min-h-0 pb-1">
            {userPicks.length <= 8 ? (
              // Grid layout for ≤8 picks - 2 columns, up to 4 rows
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 auto-rows-fr">
                {userPicks.map((pick) => (
                  <PickCard
                    key={pick.id}
                    pick={pick}
                    onRemovePick={onRemovePick}
                    onPickClick={handlePickClick}
                    onHandicapperClick={handleHandicapperClick}
                  />
                ))}
              </div>
            ) : (
              // Horizontal scrolling layout for >8 picks - maintain 4-row height
              <div className="relative h-full">
                <div
                  ref={scrollContainerRef}
                  className="h-full overflow-x-auto overflow-y-hidden parlay-scroll"
                  onScroll={handleScrollEvent}
                  onLoad={updateScrollButtons}
                >
                  <div className="flex gap-4 sm:gap-6 h-full pb-1">
                    {Array.from(
                      { length: Math.ceil(userPicks.length / 4) },
                      (_, columnIndex) => (
                        <div
                          key={columnIndex}
                          className="flex flex-col gap-4 sm:gap-6 flex-shrink-0"
                          style={{
                            width: `calc(50% - 0.75rem)`,
                            minWidth: `calc(50% - 0.75rem)`,
                          }}
                        >
                          {userPicks
                            .slice(columnIndex * 4, (columnIndex + 1) * 4)
                            .map((pick) => (
                              <PickCard
                                key={pick.id}
                                pick={pick}
                                onRemovePick={onRemovePick}
                                onPickClick={handlePickClick}
                                onHandicapperClick={handleHandicapperClick}
                              />
                            ))}
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Optimize Parlays Button Container */}
          <div className="mt-6 sm:mt-8 pb-6 sm:pb-8 flex justify-center">
            <button
              onClick={onOptimizeParlays}
              disabled={isOptimizing || totalPicksCount < 2}
              className={`px-8 sm:px-12 py-4 sm:py-6 font-bold rounded-lg text-lg sm:text-2xl transition-all ease-linear duration-300 shadow-lg hover:shadow-xl ${
                isOptimizing || totalPicksCount < 2
                  ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                  : "bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:cursor-pointer"
              }`}
            >
              {isOptimizing ? "Optimizing..." : "Optimize Parlays"}
            </button>
          </div>

          {/* Error Display */}
          {optimizerError && (
            <div className="mt-4 p-4 bg-red-900 bg-opacity-50 border border-red-500 rounded-xl">
              <p className="text-red-400 text-center">
                <strong>Error:</strong> {optimizerError}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Pick Detail Modal */}
      <PickDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        pick={selectedPick ? transformPickForModal(selectedPick) : null}
      />
    </div>
  );
}

export default HeroSection;
