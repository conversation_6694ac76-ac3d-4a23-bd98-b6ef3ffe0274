@import "tailwindcss";

/* Custom scrollbar styles for handicapper grids - updated to full length */
.handicapper-scroll {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #1f2937;
  padding-right: 8px; /* Add padding to move content away from scrollbar */
}

.handicapper-scroll::-webkit-scrollbar {
  width: 8px; /* Full length scrollbar */
  height: 8px;
}

.handicapper-scroll::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
  margin-right: 2px; /* Add margin to move scrollbar further right */
}

.handicapper-scroll::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

.handicapper-scroll::-webkit-scrollbar-thumb:hover {
  background: #60a5fa;
}

/* Custom scrollbar styles for picks dropdown - converted to blue theme */
.picks-dropdown-scroll {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #1f2937;
  padding-right: 8px;
}

.picks-dropdown-scroll::-webkit-scrollbar {
  width: 8px; /* Full length scrollbar */
  height: 8px;
}

.picks-dropdown-scroll::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
  margin-right: 2px;
}

.picks-dropdown-scroll::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

.picks-dropdown-scroll::-webkit-scrollbar-thumb:hover {
  background: #60a5fa;
}

/* Custom scrollbar styles for parlay sections - updated to full length */
.parlay-scroll {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #1f2937;
}

.parlay-scroll::-webkit-scrollbar {
  width: 8px; /* Full length scrollbar */
  height: 8px;
}

.parlay-scroll::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

.parlay-scroll::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

.parlay-scroll::-webkit-scrollbar-thumb:hover {
  background: #60a5fa;
}

/* Global blue scrollbar for all elements without custom scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 #1f2937;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
  background: #60a5fa;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
