import React, { useState } from "react";
import { getConfidenceColor } from "../../utils/colorUtils";
import PickTooltip from "../../components/Tooltips/PickTooltip";
import PickTooltipContent from "../../components/Tooltips/PickTooltipContent";

interface SubparlayPick {
  pID: string | number;
  odds: string;
  confidence?: number;
  name?: string;
  bayesian_conf?: number;
  bayesian_prob?: number;
  logistic_prob?: number;
  capital_limit?: number;
  event_id?: string;
  gameID?: number;
  league?: string;
  reusable?: boolean;
  stat_type?: string;
}

interface SubparlayColumn extends Array<SubparlayPick> {}

interface ExportOption {
  id: string;
  logoSrc?: string;
  exportType: "Promo" | "Best Line Value" | "Discount";
  exporterName: string;
  subparlays: SubparlayColumn[];
}

interface WhereToBetProps {
  subparlays: SubparlayColumn[];
}

function WhereToBet({ subparlays }: WhereToBetProps) {
  // Don't render if no parlays have been optimized yet
  if (!subparlays || subparlays.length === 0) {
    return null;
  }

  const [activePick, setActivePick] = useState<SubparlayPick | null>(null);
  const [tooltipPos, setTooltipPos] = useState<{ x: number; y: number } | null>(null);

  const mockExportOptions: ExportOption[] = [
    {
      id: "draftkings",
      logoSrc: "/draft-kings-logo.png",
      exportType: "Promo",
      exporterName: "Draft Kings",
      subparlays: subparlays,
    },
    {
      id: "prizepicks",
      logoSrc: "/prize-picks-logo.png",
      exportType: "Best Line Value",
      exporterName: "PrizePicks",
      subparlays: subparlays,
    },
    {
      id: "sleeper",
      logoSrc: "/sleeper-logo.png",
      exportType: "Discount",
      exporterName: "Sleeper",
      subparlays: subparlays,
    },
  ];

  const getExportTypeColor = (exportType: string): string => {
    switch (exportType) {
      case "Promo":
        return "#58C612";
      case "Best Line Value":
        return "#FFD700";
      case "Discount":
        return "#00D8FF";
      default:
        return "#58C612";
    }
  };

  return (
    <div className="min-h-[40vh] bg-[#051844] flex flex-col items-center justify-center mt-8 sm:mt-12 pt-2 px-4 pb-4 sm:pt-3 sm:px-6 sm:pb-6 relative z-[50] overflow-visible">
      <div className="w-full max-w-7xl mx-auto">
        <div className="space-y-4 sm:space-y-6">
          {mockExportOptions.map((option) => (
            <div
              key={option.id}
              className="flex flex-col lg:grid lg:grid-cols-[minmax(120px,150px)_minmax(160px,200px)_minmax(0,1fr)_minmax(140px,160px)] gap-4 lg:gap-6 items-center bg-[#051844] lg:bg-transparent rounded-lg lg:rounded-none p-4 lg:p-0"
            >
              {/* Logo Panel */}
              <div className="bg-[#233e6c] rounded-lg p-4 w-full lg:w-auto h-20 sm:h-24 flex items-center justify-center">
                {option.logoSrc ? (
                  <img
                    src={option.logoSrc}
                    alt={`${option.exporterName} logo`}
                    className="max-h-full max-w-full object-contain"
                  />
                ) : (
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xs text-center">
                      Logo
                      <br />
                      Placeholder
                    </span>
                  </div>
                )}
              </div>

              {/* Export Type Panel */}
              <div className="bg-[#233e6c] rounded-lg p-4 w-full lg:w-auto h-20 sm:h-24 flex items-center justify-center">
                <div
                  className="text-lg sm:text-2xl font-bold text-center leading-tight"
                  style={{ color: getExportTypeColor(option.exportType) }}
                >
                  {option.exportType}
                </div>
              </div>

              {/* Horizontal Parlay Scroll View */}
              <div className="bg-[#233e6c] rounded-lg p-4 w-full max-w-full min-h-[120px] sm:min-h-[160px] order-last lg:order-none relative overflow-visible">
                {option.subparlays.length > 0 ? (
                  <div
                    className={"overflow-x-auto parlay-scroll relative z-[1] overflow-y-visible"}
                  >
                    <div
                      className={`flex gap-2 sm:gap-3 ${
                        option.subparlays.length <= 6
                          ? "justify-center"
                          : "justify-start"
                      } items-center relative overflow-visible`}
                      style={{
                        minWidth:
                          option.subparlays.length > 6
                            ? `${option.subparlays.length * 64}px`
                            : "auto",
                      }}
                    >
                      {option.subparlays.map((column, i) => (
                        <div
                          key={i}
                          data-parlay-column={i}
                          className="flex flex-col items-center flex-shrink-0 w-10 sm:w-12 relative overflow-visible z-[1]"
                          onMouseEnter={(e) => {
                            const columnEl = e.currentTarget.closest(
                              "[data-parlay-column]"
                            );
                            if (columnEl) {
                              (columnEl as HTMLElement).style.zIndex = "999999";
                            }
                          }}
                          onMouseLeave={(e) => {
                            const columnEl = e.currentTarget.closest(
                              "[data-parlay-column]"
                            );
                            if (columnEl) {
                              (columnEl as HTMLElement).style.zIndex = "1";
                            }
                          }}
                        >
                          <div className="text-white text-xs font-bold mb-1">
                            P{i + 1}
                          </div>
                          <div className="flex flex-col gap-1 w-full relative overflow-visible">
                            {column.map((pick) => (
                              <div
                                key={pick.pID}
                                className="h-2 sm:h-3 w-full rounded text-xs flex items-center justify-center text-white font-mono cursor-pointer transition-all duration-200 hover:scale-110 hover:shadow-lg relative group overflow-visible z-[1]"
                                style={{
                                  backgroundColor: getConfidenceColor(
                                    pick.confidence
                                  ),
                                }}
                                onMouseEnter={(e) => {
                                  const columnEl = e.currentTarget.closest(
                                    "[data-parlay-column]"
                                  );
                                  if (columnEl) {
                                    (columnEl as HTMLElement).style.zIndex = "999999";
                                  }
                                  const rect = e.currentTarget.getBoundingClientRect();
                                  setActivePick(pick);
                                  setTooltipPos({ x: rect.left + rect.width / 2, y: rect.top });
                                }}
                                onMouseLeave={(e) => {
                                  const columnEl = e.currentTarget.closest(
                                    "[data-parlay-column]"
                                  );
                                  if (columnEl) {
                                    (columnEl as HTMLElement).style.zIndex = "1";
                                  }
                                  setActivePick(null);
                                }}
                              ></div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <span className="text-gray-400 text-sm">
                      No parlays available
                    </span>
                  </div>
                )}
              </div>

              {/* Export Button */}
              <div className="flex items-center justify-center w-full lg:w-auto">
                <button className="w-full lg:w-32 h-16 sm:h-20 bg-[#233e6c] hover:bg-[#1a2d54] text-white font-bold rounded-lg text-sm sm:text-base transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer flex items-center justify-center text-center leading-tight">
                  Place On
                  <br />
                  {option.exporterName}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
      {activePick && tooltipPos && (
        <PickTooltip left={tooltipPos.x} top={tooltipPos.y}>
          <PickTooltipContent pick={activePick} />
        </PickTooltip>
      )}
    </div>
  );
}

export default WhereToBet;
